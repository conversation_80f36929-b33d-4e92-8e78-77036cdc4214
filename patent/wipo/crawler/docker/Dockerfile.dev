FROM public.ecr.aws/docker/library/rust:1.86.0-bookworm as builder

WORKDIR /app
COPY . .

RUN cargo build --release --package wipo-crawler

FROM ghcr.io/browserless/chromium:v2.32.0

USER root
RUN apt-get update 

WORKDIR /app

# ビルド済みのバイナリをコピー
COPY --from=builder /app/target/release/wipo-crawler /app/wipo-crawler

# 環境変数を設定
ENV PATENT_DAYS=7

# デモ用の設定値
COPY tests/ /app/tests/
ENV DEMO=true
ENV IS_LOCAL=true

CMD ["/app/wipo-crawler"]