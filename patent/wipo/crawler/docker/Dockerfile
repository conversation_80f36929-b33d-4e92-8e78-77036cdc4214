FROM public.ecr.aws/docker/library/rust:1.86.0-bookworm as builder

WORKDIR /app
COPY . .

RUN cargo build --release --package wipo-crawler

FROM public.ecr.aws/debian/debian:bookworm-20241111-slim

# Chromeブラウザとその依存関係をインストール
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    wget \
    gnupg \
    software-properties-common \
    # Chrome の依存関係
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    libatspi2.0-0 \
    libgtk-3-0 \
    # X11 関連
    xvfb \
    # その他の依存関係
    fonts-liberation \
    libappindicator3-1 \
    && rm -rf /var/lib/apt/lists/*

# Google Chrome の公式リポジトリを追加してインストール
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# ビルド済みのバイナリをコピー
COPY --from=builder /app/target/release/wipo-crawler /app/wipo-crawler

# Chrome用の環境変数を設定
ENV CHROME_BIN=/usr/bin/google-chrome-stable
ENV CHROME_PATH=/usr/bin/google-chrome-stable

CMD ["/app/wipo-crawler"]