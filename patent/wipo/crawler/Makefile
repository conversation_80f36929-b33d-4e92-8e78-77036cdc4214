.PHONY: build-prod build-dev build-test ecr-login run-dev run-ecs-test run-ecs-prod run-demo clean

# 開発用（ローカルファイル使用）
run-dev:
	cd docker && docker compose up --build wipo-crawler-dev

# ローカルでの直接実行（開発用）
run-local:
	DEMO=true IS_LOCAL=true LIMIT=1 cargo run

# ECS用のローカルテスト（AWS認証情報使用）
run-local-s3:
	AWS_PROFILE=opendata-sandbox DEMO=false IS_LOCAL=false LIMIT=1 ADP_BUCKET_NAME=adp-workflow-interface-sandbox cargo run

# ECRログイン
ecr-login:
	aws ecr get-login-password --region ap-northeast-1 --profile ${AWS_PROFILE} | docker login --username AWS --password-stdin 849110403593.dkr.ecr.ap-northeast-1.amazonaws.com

# 本番用ビルド・タグ・プッシュ
build-sandbox:
	cd docker && ENV=sandbox docker buildx bake -f docker-compose.prod.yml --provenance=false
	docker tag sandbox-patent-scraper:wipo 849110403593.dkr.ecr.ap-northeast-1.amazonaws.com/sandbox-patent-scraper:wipo
	docker push 849110403593.dkr.ecr.ap-northeast-1.amazonaws.com/sandbox-patent-scraper:wipo

run-test:
	cd docker && docker compose -f docker-compose.yml up wipo-crawler-test

# ログを確認
logs:
	cd docker && docker compose logs -f

# コンテナの状態を確認
status:
	docker ps -a
	docker images | grep wipo-crawler
